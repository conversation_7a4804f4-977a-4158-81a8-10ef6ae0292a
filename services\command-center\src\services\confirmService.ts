
import { decryptData } from '../../../utils/cryptoUtils';
import { confirmAccount } from './CognitoService';
import { logger } from '../../../utils/logger';
import * as dotenv from 'dotenv';
import {
  CognitoIdentityProviderClient,
  ListUsersCommand,
} from '@aws-sdk/client-cognito-identity-provider';

// Initialize Cognito client
const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'us-east-1', // e.g., 'us-east-1'
});

export interface ConfirmationResult {
  success: boolean;
  message: string;
  error?: string;
  data?: {
    userId: string;
    email?: string;
    username?: string;
  };
}

dotenv.config()

export async function confirmEmailService(token: string): Promise<ConfirmationResult> {
  try {

    // First, decrypt and validate the token
    let username: string;
    try {
      logger.info('Attempting token decryption', {
        tokenLength: token.length,
        tokenPreview: token.substring(0, 20) + '...'
      });

      username = decryptData(token);
      console.log(username, "username");

      if (!username) {
        logger.error('Token decryption returned empty username');
        return {
          success: false,
          message: 'Invalid confirmation token',
          error: 'INVALID_TOKEN'
        };
      }

      logger.info('Token decryption successful', {
        username: username,
        usernameLength: username.length
      });

    } catch (decryptError: any) {
      logger.error('Token decryption failed with detailed error', {
        error: decryptError.message,
        code: decryptError.code,
        stack: decryptError.stack,
        tokenLength: token.length,
        tokenFormat: token.includes(':') ? 'colon_separated' : 'other_format'
      });

      if (decryptError.code === 'LINK_EXPIRED') {
        return {
          success: false,
          message: 'Confirmation link has expired. Please request a new confirmation email.',
          error: 'LINK_EXPIRED'
        };
      }

      return {
        success: false,
        message: 'Invalid confirmation link - decryption failed',
        error: 'DECRYPTION_FAILED'
      };
    }

    // Use the existing Cognito confirmation service
    logger.info('Attempting Cognito account confirmation', {
      username: username,
      tokenLength: token.length
    });

    // Convert sub to username
    logger.info('Looking up username by sub', { sub: username });
    const SubToUsername = await getUsernameBySub(username);
    console.log(SubToUsername, "SubToUsername");
    if (!SubToUsername) {
      logger.error('Failed to find username for sub', { sub: username });
      return {
        success: false,
        message: 'User not found in Cognito',
        error: 'USER_NOT_FOUND'
      };
    }

    logger.info('Found username for sub', {
      sub: username,
      cognitoUsername: SubToUsername
    });

    const cognitoResult = await confirmAccount(SubToUsername);

    logger.info('Cognito confirmation result', {
      status: cognitoResult.status,
      message: cognitoResult.message,
      error: cognitoResult.error,
      hasData: !!cognitoResult.data
    });

    if (cognitoResult.status === 1) {
      // Success
      return {
        success: true,
        message: 'Account confirmed successfully',
        data: {
          userId: username,
          username: username
        }
      };
    } else if (cognitoResult.status === 3) {
      // Link expired
      return {
        success: false,
        message: cognitoResult.message,
        error: 'LINK_EXPIRED'
      };
    } else {
      // Other errors
      logger.error('Cognito confirmation failed', {
        status: cognitoResult.status,
        message: cognitoResult.message,
        error: cognitoResult.error
      });

      return {
        success: false,
        message: cognitoResult.message ?? 'Account confirmation failed',
        error: cognitoResult.error ?? 'CONFIRMATION_FAILED'
      };
    }

  } catch (err) {
    logger.error('Unexpected error in confirmation service:', {
      error: err instanceof Error ? err.message : 'Unknown error',
      stack: err instanceof Error ? err.stack : undefined,
      errorType: err instanceof Error ? err.constructor.name : typeof err,
      tokenLength: token?.length || 0,
      hasEncryptionKey: !!process.env.ENCRYPTION_KEY,
      hasUserPoolId: !!process.env.USERPOOLID
    });

    // Provide more specific error messages based on the error type
    if (err instanceof Error) {
      if (err.message.includes('ENCRYPTION_KEY')) {
        return {
          success: false,
          message: 'Server configuration error - encryption key missing',
          error: 'CONFIG_ERROR'
        };
      }

      if (err.message.includes('USERPOOLID') || err.message.includes('UserPoolId')) {
        return {
          success: false,
          message: 'Server configuration error - user pool not configured',
          error: 'CONFIG_ERROR'
        };
      }

      if (err.message.includes('AWS') || err.message.includes('Cognito')) {
        return {
          success: false,
          message: 'Authentication service error',
          error: 'AUTH_SERVICE_ERROR'
        };
      }
    }

    return {
      success: false,
      message: 'Internal error during account confirmation',
      error: 'INTERNAL_ERROR'
    };
  }
}

async function getUsernameBySub(sub: string): Promise<string | null> {
  console.log(sub, "sub");
  
  if (!process.env.USERPOOLID) {
    logger.error('USERPOOLID environment variable is not set');
    return null;
  }

  try {
    const command = new ListUsersCommand({
      UserPoolId: process.env.USERPOOLID,
      Filter: `sub = "${sub}"`,
      Limit: 1,
    });

    const response = await cognitoClient.send(command);
    const user = response.Users?.[0];
    console.log(user, "user");
    
    const username = user?.Username || null;

    if (username) {
      logger.info('Successfully found username for sub', {
        sub,
        username,
        userStatus: user?.UserStatus,
        enabled: user?.Enabled
      });
    } else {
      logger.warn('No username found for sub', { sub });
    }

    return username;

  } catch (error) {
    logger.error('Error searching for user by sub in Cognito', {
      sub,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return null;
  }
}
